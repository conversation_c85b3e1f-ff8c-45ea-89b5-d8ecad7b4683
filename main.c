#include "stm32f4xx.h"
#include "stdio.h"
#include "usart.h"
#include "delay.h"
#include "led.h"
#include "G.h"
#include "timer.h"
#include "math.h"
#include "arm_math.h"
#include "kalman.h"
#include "fft.h"
#include "adc.h"
#include "dac.h"
#include "AD9833.h"
#include "lcd.h"
#include "stm32f4_key.h"
#include "touch.h"
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

// Function prototypes match the declarations in adc.h
void QCZ_FFT(volatile uint16_t* buff);
void QCZ_FFT1(volatile uint16_t* buff);

// Global variables from your project
bool Separate = false;
extern int t;
extern float fft_outputbuf[FFT_LENGTH];
extern u8 Res;
uint32_t frequency_A, frequency_B;
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A, phase_B, phase;
extern float frequency;
double current_output_freq_A, current_output_freq_B;
float phase_A_CS = 0.0f;
float phase_B_CS = 0.0f;
float phase_A_SX = 0.0f;
float phase_B_SX = 0.0f;
uint16_t current_phase_B = 0;
uint32_t peak_idx;
extern uint32_t peak1_idx, peak2_idx;

// Extern declarations now match the original types in your header files
extern volatile uint16_t buff_adc[];
extern volatile uint16_t buff_adc2[];
extern volatile uint16_t buff_adc3[];

// CORRECTED: Declarations are now on separate lines to match adc.h
extern volatile u8 flag_ADC;
extern volatile u8 flag_ADC1;
extern volatile u8 flag_ADC2;

extern float sampfre;
extern arm_cfft_radix4_instance_f32 scfft;

u8 QCZ = 100;
u8 QCZ1 = 0;
int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase = 0;
float ZE;
int SBP = 0;

uint16_t waveform_A, waveform_B;
uint16_t waveform_A_prime, waveform_B_prime;

char lcd_buffer[50];

// 频率显示格式化函数
void format_frequency_display(float freq, char* buffer) {
    if (freq >= 1000000.0f) {
        // 显示为MHz
        sprintf(buffer, "%.2f MHz", freq / 1000000.0f);
    } else if (freq >= 1000.0f) {
        // 显示为kHz
        sprintf(buffer, "%.1f kHz", freq / 1000.0f);
    } else {
        // 显示为Hz
        sprintf(buffer, "%.0f Hz", freq);
    }
}

// 频率控制变量
float current_frequency = 100.0;  // 当前频率，从100Hz开始
uint8_t key0_pressed = 0;         // PE4按键按下标志
uint8_t key1_pressed = 0;         // PE3按键按下标志
uint8_t frequency_changed = 1;    // 频率改变标志，用于更新显示
uint8_t dac_multiplier_changed = 1; // DAC倍数改变标志
uint8_t dac_enable_changed = 1;   // DAC使能改变标志
uint8_t adc_enable_changed = 1;   // ADC使能改变标志
uint8_t adc_user_enabled = 0;     // ADC用户使能标志（按钮控制）
// DAC使能状态通过DAC模块的dac_user_enabled变量控制
uint8_t selected_button = 0;      // 当前选中的按钮索引

// ADC1采样数据存储 - 优化内存使用
#define ADC1_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc1_sample_buffer[ADC1_SAMPLE_SIZE];  // ADC1采样数据缓冲区
volatile uint16_t adc1_sample_index = 0;       // 当前采样索引
volatile uint8_t adc1_sampling_complete = 0;   // 采样完成标志

// ADC2采样数据存储 - 优化内存使用
#define ADC2_SAMPLE_SIZE 1024  // 从4096减少到1024，节省6KB内存
uint16_t adc2_sample_buffer[ADC2_SAMPLE_SIZE];  // ADC2采样数据缓冲区
volatile uint16_t adc2_sample_index = 0;       // 当前采样索引
volatile uint8_t adc2_sampling_complete = 0;   // 采样完成标志

// ADC3采样数据存储 - 用于谐波分析
#define ADC3_SAMPLE_SIZE 4096  // ADC3使用4096点进行FFT谐波分析
uint16_t adc3_sample_buffer[ADC3_SAMPLE_SIZE];  // ADC3采样数据缓冲区
volatile uint16_t adc3_sample_index = 0;       // 当前采样索引
volatile uint8_t adc3_sampling_complete = 0;   // 采样完成标志
volatile uint8_t adc3_user_enabled = 0;        // ADC3用户使能标志

char lcd_buffer[50];              // LCD显示缓冲区

// ADC1采样控制函数声明
void ADC1_StartSampling(void);
void ADC1_StopSampling(void);
void ADC1_ResetSampling(void);

// ADC2采样控制函数声明
void ADC2_StartSampling(void);
void ADC2_StopSampling(void);
void ADC2_ResetSampling(void);

// ADC3采样控制函数声明
void ADC3_StartSampling(void);
void ADC3_StopSampling(void);
void ADC3_ResetSampling(void);

// ADC3谐波分析函数声明
void ProcessADC3HarmonicAnalysis(void);
void OutputHarmonicResults(void);

// 扫频测试相关变量和函数声明
typedef struct {
    float frequency;        // 当前频率
    float adc1_amplitude;   // ADC1幅度（滤波器输入）
    float adc2_amplitude;   // ADC2幅度（滤波器输出）
    float magnitude_db;     // 幅度响应(dB)
    float phase_deg;        // 相位响应(度)
} FrequencyResponse;

#define SWEEP_POINTS 1996   // 扫频点数：(400kHz-1kHz)/200Hz + 1 = 1996
#define SWEEP_POINTS_100K 496  // 1kHz到100kHz的扫频点数：(100kHz-1kHz)/200Hz + 1 = 496
#define SWEEP_BUFFER_SIZE 50  // 只保存最近50个点的结果用于分析
#define SMOOTH_FILTER_SIZE 5  // 平滑滤波器窗口大小
FrequencyResponse sweep_results[SWEEP_BUFFER_SIZE];  // 减少内存使用：50×20字节=1KB
volatile uint8_t sweep_test_active = 0;
volatile uint16_t current_sweep_point = 0;
volatile uint8_t sweep_sampling_complete = 0;
volatile uint16_t total_sweep_points = 0;  // 总扫频点数计数器

// 归一化处理相关变量
float max_voltage_ratio = 0.0f;           // 最大电压幅度比
volatile uint8_t sweep_phase = 0;          // 扫频阶段：0=低频检测，1=完整扫频，2=归一化输出

// 平滑滤波器相关变量
float smooth_buffer_phase2[SMOOTH_FILTER_SIZE];  // 第二次扫频平滑缓冲区
float smooth_buffer_phase3[SMOOTH_FILTER_SIZE];  // 第三次扫频平滑缓冲区
uint8_t smooth_index_phase2 = 0;                 // 第二次扫频平滑缓冲区索引
uint8_t smooth_index_phase3 = 0;                 // 第三次扫频平滑缓冲区索引
uint8_t smooth_count_phase2 = 0;                 // 第二次扫频平滑缓冲区有效数据计数
uint8_t smooth_count_phase3 = 0;                 // 第三次扫频平滑缓冲区有效数据计数

// 滤波器类型判断相关变量
float freq_1kHz_ratio = 0.0f;             // 1kHz频率点的归一化电压幅度比
float freq_1_2kHz_ratio = 0.0f;           // 1.2kHz频率点的归一化电压幅度比
float freq_399_8kHz_ratio = 0.0f;         // 399.8kHz频率点的归一化电压幅度比
float freq_400kHz_ratio = 0.0f;           // 400kHz频率点的归一化电压幅度比

// 第一次扫频的低频检测结果
float first_sweep_1kHz_ratio = 0.0f;      // 第一次扫频1kHz的电压幅度比
float first_sweep_1_2kHz_ratio = 0.0f;    // 第一次扫频1.2kHz的电压幅度比
uint8_t amplitude_multiplier = 1;         // 电压幅度比倍数（1或2）
uint8_t low_freq_points_completed = 0;    // 低频点完成计数

// 扫频测试函数声明
void StartSweepTest(void);
void StopSweepTest(void);
void ProcessSweepPoint(void);
void OutputSweepResults(void);
void AnalyzeFilterCharacteristics(void);
void DetermineFilterType(void);

// 传递函数和参数计算函数声明
void IdentifyCircuitModel(void);
void CalculateFilterParameters(void);
void DisplayTransferFunction(void);
void DisplayFilterParameters(float K, float omega_0, float Q, const char* filter_type);
float FindCutoffFrequency(void);
float CalculateQFactor(float f0, float f1, float f2);

// 平滑滤波函数声明
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size);
void InitSmoothFilters(void);

// 扫频测试函数声明（简化版）

// 虚拟按钮定义
typedef struct {
    uint16_t x;      // 按钮左上角X坐标
    uint16_t y;      // 按钮左上角Y坐标
    uint16_t width;  // 按钮宽度
    uint16_t height; // 按钮高度
    char* text;      // 按钮文字
    float freq_step; // 频率步进值
    uint16_t color;  // 按钮颜色
} Button_t;

// 定义八个按钮 - 更大尺寸便于操作
Button_t buttons[8] = {
    // 第一行：频率调整按钮
    {5,   130, 90, 60, "+100kHz", 100000.0f, BLUE},
    {100, 130, 90, 60, "+10kHz",  10000.0f,  GREEN},
    {195, 130, 90, 60, "+1kHz",   1000.0f,   ORANGE},
    {290, 130, 90, 60, "+100Hz",  100.0f,    RED},
    // 第二行：DAC和ADC控制按钮
    {5,   200, 90, 60, "DAC OFF",  0.0f,     GRAY},     // DAC开关按钮
    {100, 200, 90, 60, "DAC x1.0", 0.0f,     MAGENTA},  // DAC倍数按钮
    {195, 200, 90, 60, "SWEEP OFF", 0.0f,     GRAY},     // 扫频测试按钮
    {290, 200, 90, 60, "ADC3 OFF", 0.0f,     GRAY}      // ADC3谐波分析按钮
};

// 绘制按钮函数 - 支持选中和按下状态
void draw_button(Button_t* btn, uint8_t pressed, uint8_t selected) {
    uint16_t bg_color, text_color, border_color;

    if (pressed) {
        // 按下状态：红色背景，白色文字
        bg_color = RED;
        text_color = WHITE;
        border_color = RED;
    } else if (selected) {
        // 选中状态：蓝色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLUE;
    } else {
        // 正常状态：黑色边框，黑色文字
        bg_color = WHITE;
        text_color = BLACK;
        border_color = BLACK;
    }

    // 绘制按钮背景
    lcd_fill(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, bg_color);

    // 绘制按钮边框
    lcd_draw_rectangle(btn->x, btn->y, btn->x + btn->width, btn->y + btn->height, border_color);

    // 如果是选中状态，绘制双重边框
    if (selected && !pressed) {
        lcd_draw_rectangle(btn->x + 1, btn->y + 1, btn->x + btn->width - 1, btn->y + btn->height - 1, border_color);
    }

    // 计算文字居中位置
    uint16_t text_len = strlen(btn->text);
    uint16_t text_x = btn->x + (btn->width - text_len * 6) / 2;  // 16号字体宽度约6像素
    uint16_t text_y = btn->y + (btn->height - 16) / 2;          // 16号字体高度16像素

    // 保存当前画笔颜色
    uint32_t old_color = g_point_color;

    // 设置文字颜色并显示按钮文字
    g_point_color = text_color;
    lcd_show_string(text_x, text_y, btn->width, btn->height, 16, btn->text, text_color);

    // 恢复画笔颜色
    g_point_color = old_color;
}

// 绘制所有按钮
void draw_all_buttons(uint8_t selected_index) {
    for (int i = 0; i < 8; i++) {
        draw_button(&buttons[i], 0, (i == selected_index) ? 1 : 0);
    }
}

// 检测按钮点击
int check_button_press(uint16_t touch_x, uint16_t touch_y) {
    for (int i = 0; i < 8; i++) {
        if (touch_x >= buttons[i].x && touch_x <= (buttons[i].x + buttons[i].width) &&
            touch_y >= buttons[i].y && touch_y <= (buttons[i].y + buttons[i].height)) {
            return i;  // 返回按钮索引
        }
    }
    return -1;  // 没有按钮被按下
}



// 频率调整函数
void adjust_frequency(float step) {
    current_frequency += step;

    // 检查频率范围
    if (current_frequency > 1200000.0f) {
        current_frequency = 100.0f;  // 回到100Hz
    } else if (current_frequency < 100.0f) {
        current_frequency = 100.0f;  // 最小100Hz
    }

    // 设置AD9833新的频率
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 只有在DAC开启时才设置DAC正弦波频率
    if (DAC_GetUserEnable()) {
        DAC_SetSineFrequency(current_frequency);
    }

    frequency_changed = 1;
}

int main(void)
{
    arm_cfft_radix4_init_f32(&scfft, FFT_LENGTH, 0, 1);
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(112500);
    delay_init(168);

    // 串口测试输出
    printf("System Starting...\r\n");
    delay_ms(100);

    LED_Init();
    Adc_Init();
    Adc2_Init();     // ADC2配置为PC1引脚（ADC123_IN11通道11）
    DAC_PA4_Init();  // PA4配置为DAC而不是ADC2
    DAC_SineWave_Init();  // 初始化DAC正弦波功能
    DAC_SetUserEnable(0); // 初始状态DAC用户禁用

    Adc3_Init();

    // 初始状态关闭ADC
    ADC_Cmd(ADC1, DISABLE);
    ADC_Cmd(ADC2, DISABLE);
    ADC_Cmd(ADC3, DISABLE);
    // 扫频测试使用中断方式采样，不需要DMA
    // DMA1_Init();  // ADC1使用中断采样，不需要DMA
    // DMA2_Init();  // ADC2使用中断采样，不需要DMA
    // DMA3_Init();  // ADC3也使用中断采样，不需要DMA
    AD9833_Init();
    AD9833_Init1();
    key_config();  // 初始化按键

    lcd_init();
   
    sampfre = 815534;  // 实际采样频率：84MHz / 103 / 1 = 815534Hz

    TIM3_Int_Init(103 - 1, 1 - 1);  // 84MHz / 103 / 1 = 815534Hz ≈ 819200Hz，用于ADC触发
    TIM4_Int_Init(1000 - 1, 8400 - 1);
    TIM_Cmd(TIM3, ENABLE);

    // 初始化TIM6用于DAC DMA触发 (频率会根据输出频率动态调整)
    // 初始配置：84MHz / 1 / 105 = 800kHz (会被DAC_SetSineFrequency动态更新)
    TIM6_DAC_Init(105 - 1, 1 - 1);

    // UI Redesign for better aesthetics and clarity
    lcd_clear(WHITE);
    g_point_color = BLACK;

    // 删除未使用的变量以消除编译警告

    // 设置默认画笔颜色
    g_point_color = BLACK;

    // 显示标题和操作提示
    lcd_show_string(10, 30, lcddev.width, 30, 16, "Frequency_out:", BLACK);

    // 绘制频率控制按钮（默认选中第一个）
    draw_all_buttons(selected_button);


    // 设置AD9833通道一产生100Hz正弦波
    AD9833_SetFrequencyQuick1(current_frequency, AD9833_OUT_SINUS1);

    // 设置DAC输出相同频率的正弦波 (0-1V范围)
    DAC_SetSineFrequency(current_frequency);

    // 立即显示初始频率
    g_point_color = BLACK;
    format_frequency_display(current_frequency, lcd_buffer);
    uint16_t str_len = strlen(lcd_buffer);
    uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
    lcd_show_string(x_pos, 80, lcddev.width, 30, 16, lcd_buffer, BLACK);



    // 显示初始选中的按钮
    sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
    lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

    // 标记频率已显示
    frequency_changed = 0;

    while (1)
    {
        // 检测PE4按键（KEY0）- 移动选择按钮
        if (KEY0 == 0)  // 按键按下（低电平有效）
        {
            if (key0_pressed == 0)  // 防止重复触发
            {
                key0_pressed = 1;

                // 移动到下一个按钮
                selected_button = (selected_button + 1) % 8;

                // 重新绘制所有按钮以更新选中状态
                draw_all_buttons(selected_button);

                // 显示当前选中的按钮信息
                sprintf(lcd_buffer, "Selected: %s (%.0f)", buttons[selected_button].text, buttons[selected_button].freq_step);
                lcd_show_string(10, 50, lcddev.width, 20, 12, lcd_buffer, BLUE);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key0_pressed = 0;  // 按键释放
        }

        // 检测PE3按键（KEY1）- 按下当前选中的按钮
        if (KEY1 == 0)  // 按键按下（低电平有效）
        {
            if (key1_pressed == 0)  // 防止重复触发
            {
                key1_pressed = 1;

                // 显示按钮按下效果
                draw_button(&buttons[selected_button], 1, 1);
                delay_ms(100);  // 显示按下效果

                // 执行按钮功能
                if (selected_button == 4) {
                    // DAC开关按钮
                    uint8_t current_dac_state = DAC_GetUserEnable();
                    DAC_SetUserEnable(!current_dac_state);
                    dac_enable_changed = 1;

                    // 更新按钮文本和颜色
                    if (DAC_GetUserEnable()) {
                        sprintf(buttons[4].text, "DAC ON");
                        buttons[4].color = GREEN;
                    } else {
                        sprintf(buttons[4].text, "DAC OFF");
                        buttons[4].color = GRAY;
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "DAC: %s", DAC_GetUserEnable() ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 5) {
                    // DAC倍数按钮 - 只有在DAC开启时才能调整
                    if (DAC_GetUserEnable()) {
                        DAC_NextAmplitudeMultiplier();
                        dac_multiplier_changed = 1;

                        // 更新按钮文本
                        float multiplier = DAC_GetAmplitudeMultiplier();
                        sprintf(buttons[5].text, "DAC x%.1f", multiplier);

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "DAC Multiplier: %.1f", multiplier);
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    } else {
                        // DAC未开启时的提示
                        char debug_buffer[100];
                        sprintf(debug_buffer, "Please enable DAC first!");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }
                } else if (selected_button == 6) {
                    // ADC开关按钮 - 启动扫频测试
                    if (!sweep_test_active) {
                        // 启动扫频测试
                        sprintf(buttons[6].text, "SWEEP ON");
                        buttons[6].color = GREEN;

                        // 启动扫频测试（无串口输出）

                        // 关闭DAC
                        if (DAC_GetUserEnable()) {
                            DAC_SetUserEnable(0);
                            dac_enable_changed = 1;
                            sprintf(buttons[4].text, "DAC OFF");
                            buttons[4].color = GRAY;
                        }

                        // 启动扫频测试
                        StartSweepTest();

                    } else {
                        // 停止扫频测试
                        sprintf(buttons[6].text, "SWEEP OFF");
                        buttons[6].color = GRAY;

                        StopSweepTest();
                    }

                    // 调试信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "ADC: %s", adc_user_enabled ? "ON" : "OFF");
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                } else if (selected_button == 7) {
                    // ADC3谐波分析按钮
                    if (!adc3_user_enabled) {
                        // 启动ADC3谐波分析
                        sprintf(buttons[7].text, "ADC3 ON");
                        buttons[7].color = GREEN;
                        adc3_user_enabled = 1;

                        // 启动ADC3采样
                        ADC3_ResetSampling();
                        ADC3_StartSampling();

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "ADC3: Starting harmonic analysis");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, GREEN);

                    } else {
                        // 停止ADC3谐波分析
                        sprintf(buttons[7].text, "ADC3 OFF");
                        buttons[7].color = GRAY;
                        adc3_user_enabled = 0;

                        // 停止ADC3采样
                        ADC3_StopSampling();

                        // 调试信息
                        char debug_buffer[100];
                        sprintf(debug_buffer, "ADC3: Stopped");
                        lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);
                    }

                } else {
                    // 频率调整按钮 (0-3)
                    float step_value = buttons[selected_button].freq_step;

                    // 调试信息：显示按钮详细信息
                    char debug_buffer[100];
                    sprintf(debug_buffer, "Btn:%d Step:%.0f", selected_button, step_value);
                    lcd_show_string(10, 70, lcddev.width, 20, 12, debug_buffer, RED);

                    adjust_frequency(step_value);
                }

                // 恢复按钮正常显示
                draw_all_buttons(selected_button);

                // 延时防抖
                delay_ms(200);
            }
        }
        else
        {
            key1_pressed = 0;  // 按键释放
        }

        // 触屏功能已禁用，使用物理按键控制

        // 更新LCD显示（仅在频率改变时）
        if (frequency_changed) {
            // 清除频率显示区域（不影响按钮）
            lcd_fill(0, 60, lcddev.width, 120, WHITE);

            // 重新绘制按钮（先绘制按钮）
            draw_all_buttons(selected_button);

            // 格式化频率字符串
            format_frequency_display(current_frequency, lcd_buffer);

            // 计算居中位置
            uint16_t str_len = strlen(lcd_buffer);
            uint16_t x_pos = (lcddev.width - str_len * 8) / 2;
            uint16_t y_pos = 80;  // 在按钮上方显示

            // 保存当前画笔颜色
            uint32_t old_color = g_point_color;

            // 设置文字颜色并显示频率
            g_point_color = BLACK;
            lcd_show_string(x_pos, y_pos, lcddev.width, 30, 16, lcd_buffer, BLACK);

            // 恢复画笔颜色
            g_point_color = old_color;

            frequency_changed = 0;  // 清除改变标志

            // 显示DAC状态
            if (!DAC_GetUserEnable())
            {
                sprintf(lcd_buffer, "DAC: DISABLED");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GRAY);
            }
            else if (current_frequency <= 100000.0f)
            {
                float multiplier = DAC_GetAmplitudeMultiplier();
                sprintf(lcd_buffer, "DAC: ON (%.1fV out)", multiplier);
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, GREEN);
            }
            else
            {
                sprintf(lcd_buffer, "DAC: OFF (>100kHz)");
                lcd_show_string(10, 270, lcddev.width, 20, 12, lcd_buffer, RED);
            }
        }

        // 检查DAC使能状态是否改变
        if (dac_enable_changed)
        {
            dac_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新DAC开关按钮
            draw_all_buttons(selected_button);
        }

        // 检查DAC倍数是否改变
        if (dac_multiplier_changed)
        {
            dac_multiplier_changed = 0;  // 清除改变标志

            // 重新绘制DAC倍数按钮以更新文本
            draw_all_buttons(selected_button);
        }

        // 检查ADC使能是否改变
        if (adc_enable_changed)
        {
            adc_enable_changed = 0;  // 清除改变标志

            // 重新绘制所有按钮以更新ADC开关按钮
            draw_all_buttons(selected_button);
        }

        // 处理扫频测试
        if (sweep_test_active)
        {
            // 检查当前频率点的采样是否完成
            if (adc1_sampling_complete && adc2_sampling_complete)
            {
                // 处理当前扫频点的数据
                ProcessSweepPoint();

                // 移动到下一个频率点
                current_sweep_point++;

                // 根据扫频阶段决定下一个频率点
                uint8_t continue_sweep = 0;

                if (sweep_phase == 0) {
                    // 第一阶段：只扫1kHz和1.2kHz
                    printf("DEBUG Phase 0 frequency control: low_freq_points_completed=%d\r\n", low_freq_points_completed);

                    if (low_freq_points_completed < 2) {
                        float next_freq;
                        if (low_freq_points_completed == 1) {
                            next_freq = 1200.0f;  // 第二个点是1.2kHz
                            AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);
                            printf("DEBUG Phase 0: Set frequency to %.1f Hz\r\n", next_freq);

                            // 显示进度
                            char progress_info[100];
                            sprintf(progress_info, "Phase 1: Low Freq Test (%.1fkHz)", next_freq/1000.0f);
                            lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                            continue_sweep = 1;
                        } else {
                            printf("DEBUG Phase 0: Waiting for first point to complete\r\n");
                        }
                    } else {
                        printf("DEBUG Phase 0: Both low freq points completed\r\n");
                    }
                } else if (sweep_phase == 1) {
                    // 第二阶段：1kHz到100kHz扫频（输出幅频特性和相频特性）
                    if (current_sweep_point < SWEEP_POINTS_100K) {
                        float next_freq = 1000.0f + current_sweep_point * 200.0f;
                        AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                        // 显示进度
                        char progress_info[100];
                        sprintf(progress_info, "Phase 2: 1k-100k Sweep %d/%d (%.1fkHz)",
                                current_sweep_point, SWEEP_POINTS_100K, next_freq/1000.0f);
                        lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                        continue_sweep = 1;
                    }
                } else if (sweep_phase == 2) {
                    // 第三阶段：归一化处理（完整扫频1kHz到400kHz）
                    if (current_sweep_point < SWEEP_POINTS) {
                        float next_freq = 1000.0f + current_sweep_point * 200.0f;
                        AD9833_SetFrequencyQuick1(next_freq, AD9833_OUT_SINUS1);

                        // 显示进度
                        char progress_info[100];
                        sprintf(progress_info, "Phase 3: Normalize %d/%d (%.1fkHz)",
                                current_sweep_point, SWEEP_POINTS, next_freq/1000.0f);
                        lcd_show_string(10, 90, lcddev.width, 20, 12, progress_info, BLUE);

                        continue_sweep = 1;
                    }
                }

                if (continue_sweep) {
                    // 重新启动ADC采样
                    ADC1_ResetSampling();
                    ADC2_ResetSampling();
                    ADC1_StartSampling();
                    ADC2_StartSampling();
                }
                else
                {
                    if (sweep_phase == 0) {
                        // 第一阶段完成，判断倍数并开始第二阶段
                        if (first_sweep_1kHz_ratio > 0.0f && first_sweep_1_2kHz_ratio > 0.0f) {
                            printf("DEBUG: 1kHz=%.6f, 1.2kHz=%.6f\r\n", first_sweep_1kHz_ratio, first_sweep_1_2kHz_ratio);
                            if (first_sweep_1kHz_ratio < 0.7f && first_sweep_1_2kHz_ratio < 0.7f) {
                                amplitude_multiplier = 2;
                                printf("Low frequency ratios detected, applying 2x multiplier\r\n");
                            } else {
                                amplitude_multiplier = 1;
                                printf("Normal frequency ratios, using 1x multiplier\r\n");
                            }
                        } else {
                            amplitude_multiplier = 1;
                            printf("Insufficient low frequency data, using 1x multiplier\r\n");
                            printf("DEBUG: 1kHz=%.6f, 1.2kHz=%.6f\r\n", first_sweep_1kHz_ratio, first_sweep_1_2kHz_ratio);
                        }
                        printf("DEBUG: Final amplitude_multiplier = %d\r\n", amplitude_multiplier);

                        sweep_phase = 1;
                        current_sweep_point = 0;
                        max_voltage_ratio = 0.0f;  // 重置最大值

                        // 输出第二次扫频的数据格式说明
                        printf("=== PHASE 2: 1kHz-100kHz FREQUENCY RESPONSE ===\r\n");
                        printf("Frequency(Hz)\tAmplitude_Ratio\tMagnitude(dB)\tPhase(deg)\r\n");

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 2: 1k-100k Sweep", BLUE);
                    } else if (sweep_phase == 1) {
                        // 第二阶段完成，输出结束标记
                        printf("=== END OF PHASE 2: 1kHz-100kHz FREQUENCY RESPONSE ===\r\n");

                        // 开始第三阶段
                        sweep_phase = 2;
                        current_sweep_point = 0;

                        // 设置起始频率重新开始扫频
                        float start_freq = 1000.0f;
                        AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
                        delay_ms(10);

                        // 重新启动ADC采样
                        ADC1_ResetSampling();
                        ADC2_ResetSampling();
                        ADC1_StartSampling();
                        ADC2_StartSampling();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Phase 3: Normalize", BLUE);
                    } else {
                        // 第三阶段完成，扫频测试结束
                        StopSweepTest();
                        OutputSweepResults();

                        // 判断并显示滤波器类型
                        DetermineFilterType();

                        lcd_show_string(10, 90, lcddev.width, 20, 12, "Sweep Test Complete!", GREEN);
                    }
                }
            }
        }

        // 检查ADC1采样是否完成（非扫频模式）
        if (adc1_sampling_complete && !sweep_test_active && adc_user_enabled)
        {
            // 显示采样完成信息
            char sample_info[100];
            sprintf(sample_info, "ADC1: 4096 samples complete");
            lcd_show_string(10, 90, lcddev.width, 20, 12, sample_info, BLUE);

            // 显示一些采样数据（前几个点）
            sprintf(sample_info, "Data[0-3]: %d %d %d %d",
                    adc1_sample_buffer[0], adc1_sample_buffer[1],
                    adc1_sample_buffer[2], adc1_sample_buffer[3]);
            lcd_show_string(10, 110, lcddev.width, 20, 12, sample_info, GREEN);

            // 通过串口输出所有ADC1采样数据
            printf("ADC1_SAMPLES_START\r\n");
            for (int i = 0; i < ADC1_SAMPLE_SIZE; i++)
            {
                printf("%d\t", adc1_sample_buffer[i]);
            }
            printf("ADC1_SAMPLES_END\r\n");

            // 重置采样状态，准备下次采样
            adc1_sampling_complete = 0;
            adc1_sample_index = 0;
        }

        // 处理ADC3谐波分析
        if (adc3_user_enabled && adc3_sampling_complete)
        {
            ProcessADC3HarmonicAnalysis();
            // 重新启动采样以便连续分析
            ADC3_ResetSampling();
            ADC3_StartSampling();
        }

        delay_ms(10);  // 主循环延时
    }
}

// ADC1采样控制函数实现
void ADC1_StartSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }

    // 启动ADC1
    ADC_Cmd(ADC1, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC1_StopSampling(void)
{
    // 停止ADC1
    ADC_Cmd(ADC1, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC1_ResetSampling(void)
{
    // 重置采样状态
    adc1_sample_index = 0;
    adc1_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_sample_buffer[i] = 0;
    }
}

// ADC2采样控制函数实现
void ADC2_StartSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }

    // 启动ADC2
    ADC_Cmd(ADC2, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC2_StopSampling(void)
{
    // 停止ADC2
    ADC_Cmd(ADC2, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC2_ResetSampling(void)
{
    // 重置采样状态
    adc2_sample_index = 0;
    adc2_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC2_SAMPLE_SIZE; i++) {
        adc2_sample_buffer[i] = 0;
    }
}

// ADC3采样控制函数实现
void ADC3_StartSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区（可选）
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }

    // 启动ADC3
    ADC_Cmd(ADC3, ENABLE);

    // 如果使用定时器触发，确保定时器运行
    TIM_Cmd(TIM3, ENABLE);
}

void ADC3_StopSampling(void)
{
    // 停止ADC3
    ADC_Cmd(ADC3, DISABLE);

    // 可以选择停止定时器（如果不影响其他功能）
    // TIM_Cmd(TIM3, DISABLE);
}

void ADC3_ResetSampling(void)
{
    // 重置采样状态
    adc3_sample_index = 0;
    adc3_sampling_complete = 0;

    // 清空缓冲区
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_sample_buffer[i] = 0;
    }
}

// 扫频测试函数实现
void StartSweepTest(void)
{
    sweep_test_active = 1;
    current_sweep_point = 0;
    total_sweep_points = 0;
    sweep_sampling_complete = 0;
    max_voltage_ratio = 0.0f;
    sweep_phase = 0;  // 第一阶段：低频检测

    // 初始化滤波器类型判断相关变量
    freq_1kHz_ratio = 0.0f;
    freq_1_2kHz_ratio = 0.0f;
    freq_399_8kHz_ratio = 0.0f;
    freq_400kHz_ratio = 0.0f;

    // 初始化第一次扫频的低频检测变量
    first_sweep_1kHz_ratio = 0.0f;
    first_sweep_1_2kHz_ratio = 0.0f;
    amplitude_multiplier = 1;
    low_freq_points_completed = 0;

    // 初始化平滑滤波器
    InitSmoothFilters();

    // 清空结果缓冲区
    for (int i = 0; i < SWEEP_BUFFER_SIZE; i++) {
        sweep_results[i].frequency = 0;
        sweep_results[i].adc1_amplitude = 0;
        sweep_results[i].adc2_amplitude = 0;
        sweep_results[i].magnitude_db = 0;
        sweep_results[i].phase_deg = 0;
    }

    // 设置起始频率 1kHz
    float start_freq = 1000.0f;
    AD9833_SetFrequencyQuick1(start_freq, AD9833_OUT_SINUS1);
    printf("DEBUG StartSweepTest: Set initial frequency to %.1f Hz\r\n", start_freq);

    // 开始扫频测试，无串口输出

    // 等待频率稳定
    delay_ms(10);

    // 启动ADC1和ADC2同步采样
    ADC1_ResetSampling();
    ADC2_ResetSampling();
    ADC1_StartSampling();
    ADC2_StartSampling();
}

void StopSweepTest(void)
{
    sweep_test_active = 0;

    // 停止ADC采样
    ADC1_StopSampling();
    ADC2_StopSampling();
}

void ProcessSweepPoint(void)
{
    float current_freq = 1000.0f + current_sweep_point * 200.0f;

    // 调试信息：显示当前状态
    if (current_sweep_point < 3) {
        printf("DEBUG ProcessSweepPoint: phase=%d, point=%d, freq=%.1f, multiplier=%d\r\n",
               sweep_phase, current_sweep_point, current_freq, amplitude_multiplier);
    }

    // 计算ADC1和ADC2的RMS值（去除直流分量）
    float adc1_dc = 0, adc2_dc = 0;
    float adc1_rms = 0, adc2_rms = 0;

    // 计算直流分量
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        adc1_dc += adc1_sample_buffer[i];
        adc2_dc += adc2_sample_buffer[i];
    }
    adc1_dc /= ADC1_SAMPLE_SIZE;
    adc2_dc /= ADC2_SAMPLE_SIZE;

    // 计算RMS值（去除直流分量）
    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_ac = adc1_sample_buffer[i] - adc1_dc;
        float adc2_ac = adc2_sample_buffer[i] - adc2_dc;
        adc1_rms += adc1_ac * adc1_ac;
        adc2_rms += adc2_ac * adc2_ac;
    }
    adc1_rms = sqrtf(adc1_rms / ADC1_SAMPLE_SIZE);
    adc2_rms = sqrtf(adc2_rms / ADC2_SAMPLE_SIZE);

    // 计算幅度响应 (dB) - 输出/输入
    float magnitude_db;
    if (adc2_rms > 0 && adc1_rms > 0) {
        magnitude_db = 20.0f * log10f(adc2_rms / adc1_rms);
    } else {
        magnitude_db = -100.0f; // 很小的值
    }

    // 简化的相位计算（基于峰值位置差）
    int adc1_peak_idx = 0, adc2_peak_idx = 0;
    float adc1_max = 0, adc2_max = 0;

    for (int i = 0; i < ADC1_SAMPLE_SIZE; i++) {
        float adc1_val = fabsf(adc1_sample_buffer[i] - adc1_dc);
        float adc2_val = fabsf(adc2_sample_buffer[i] - adc2_dc);

        if (adc1_val > adc1_max) {
            adc1_max = adc1_val;
            adc1_peak_idx = i;
        }
        if (adc2_val > adc2_max) {
            adc2_max = adc2_val;
            adc2_peak_idx = i;
        }
    }

    // 计算相位差（度）
    float sample_period = 1.0f / 815534.0f; // 采样周期
    float signal_period = 1.0f / current_freq; // 信号周期
    float time_diff = (adc1_peak_idx - adc2_peak_idx) * sample_period;
    float phase_deg = (time_diff / signal_period) * 360.0f;

    // 限制相位范围到 -180 到 +180 度
    while (phase_deg > 180.0f) {
        phase_deg -= 360.0f;
    }
    while (phase_deg < -180.0f) {
        phase_deg += 360.0f;
    }

    // 计算电压幅度比（滤波器输出/输入，线性比值，不是dB）
    float voltage_ratio = 0.0f;
    if (adc1_rms > 0) {
        voltage_ratio = adc2_rms / adc1_rms;
    }

    if (sweep_phase == 0) {
        // 第一阶段：只检测1kHz和1.2kHz
        printf("DEBUG Phase 0: current_freq=%.1f, checking for 1kHz and 1.2kHz\r\n", current_freq);

        if (fabs(current_freq - 1000.0f) < 1.0f) {          // 1kHz ± 1Hz
            first_sweep_1kHz_ratio = voltage_ratio;
            low_freq_points_completed++;
            printf("1kHz ratio: %.6f (completed count: %d)\r\n", voltage_ratio, low_freq_points_completed);
        } else if (fabs(current_freq - 1200.0f) < 1.0f) {   // 1.2kHz ± 1Hz
            first_sweep_1_2kHz_ratio = voltage_ratio;
            low_freq_points_completed++;
            printf("1.2kHz ratio: %.6f (completed count: %d)\r\n", voltage_ratio, low_freq_points_completed);
        } else {
            printf("DEBUG Phase 0: Frequency %.1f not matched for low freq detection\r\n", current_freq);
        }
    } else if (sweep_phase == 1) {
        // 第二阶段：1kHz到100kHz扫频，输出频率、幅频特性和相频特性到串口
        float adjusted_voltage_ratio = voltage_ratio * amplitude_multiplier;

        // 应用平滑滤波到幅度比
        float smoothed_ratio = ApplySmoothFilter(adjusted_voltage_ratio,
                                               smooth_buffer_phase2,
                                               &smooth_index_phase2,
                                               &smooth_count_phase2,
                                               SMOOTH_FILTER_SIZE);

        // 计算幅频特性（dB）
        float magnitude_response_db = 0.0f;
        if (smoothed_ratio > 0.0f) {
            magnitude_response_db = 20.0f * log10f(smoothed_ratio);
        } else {
            magnitude_response_db = -100.0f; // 很小的值用-100dB表示
        }

        // 输出：频率(Hz) \t 幅频特性(线性比值) \t 幅频特性(dB) \t 相频特性(度)
        printf("%.1f\t%.6f\t%.2f\t%.2f\r\n", current_freq, smoothed_ratio, magnitude_response_db, phase_deg);

        // 调试信息：显示前几个点的倍数应用情况
        if (current_sweep_point < 5) {
            printf("DEBUG: Point %d, original=%.6f, multiplier=%d, adjusted=%.6f, smoothed=%.6f, phase=%.2f\r\n",
                   current_sweep_point, voltage_ratio, amplitude_multiplier, adjusted_voltage_ratio, smoothed_ratio, phase_deg);
        }

        // 寻找最大值（使用平滑后的值）
        if (smoothed_ratio > max_voltage_ratio) {
            max_voltage_ratio = smoothed_ratio;
        }
    } else {
        // 第三阶段：归一化处理，应用平滑滤波，不输出串口数据
        float normalized_ratio = 0.0f;
        float adjusted_voltage_ratio = voltage_ratio * amplitude_multiplier;

        if (max_voltage_ratio > 0.0f) {
            normalized_ratio = adjusted_voltage_ratio / max_voltage_ratio;
        }

        // 应用平滑滤波到归一化幅度比
        float smoothed_normalized_ratio = ApplySmoothFilter(normalized_ratio,
                                                          smooth_buffer_phase3,
                                                          &smooth_index_phase3,
                                                          &smooth_count_phase3,
                                                          SMOOTH_FILTER_SIZE);

        // 存储特定频率点的平滑后归一化电压幅度比用于滤波器类型判断
        if (fabs(current_freq - 1000.0f) < 1.0f) {          // 1kHz ± 1Hz
            freq_1kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 1200.0f) < 1.0f) {   // 1.2kHz ± 1Hz
            freq_1_2kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 399800.0f) < 1.0f) { // 399.8kHz ± 1Hz
            freq_399_8kHz_ratio = smoothed_normalized_ratio;
        } else if (fabs(current_freq - 400000.0f) < 1.0f) { // 400kHz ± 1Hz
            freq_400kHz_ratio = smoothed_normalized_ratio;
        }
    }

    // 只在缓冲区中保存最近的数据用于分析
    int buffer_idx = current_sweep_point % SWEEP_BUFFER_SIZE;
    sweep_results[buffer_idx].frequency = current_freq;
    sweep_results[buffer_idx].adc1_amplitude = adc1_rms;
    sweep_results[buffer_idx].adc2_amplitude = adc2_rms;
    sweep_results[buffer_idx].magnitude_db = magnitude_db;
    sweep_results[buffer_idx].phase_deg = phase_deg;

    total_sweep_points++;
}

void OutputSweepResults(void)
{
    // 扫频完成，无额外输出
}

void AnalyzeFilterCharacteristics(void)
{
    printf("=== FILTER ANALYSIS (Based on Recent Data) ===\r\n");

    // 分析缓冲区中的数据
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    if (valid_points < 5) {
        printf("Insufficient data for analysis\r\n");
        printf("=== END OF ANALYSIS ===\r\n");
        return;
    }

    // 找到最大和最小幅度
    float max_magnitude = -100.0f;
    float min_magnitude = 100.0f;
    float max_freq = 0, min_freq = 0;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].frequency > 0) {  // 有效数据
            if (sweep_results[i].magnitude_db > max_magnitude) {
                max_magnitude = sweep_results[i].magnitude_db;
                max_freq = sweep_results[i].frequency;
            }
            if (sweep_results[i].magnitude_db < min_magnitude) {
                min_magnitude = sweep_results[i].magnitude_db;
                min_freq = sweep_results[i].frequency;
            }
        }
    }

    printf("Recent Data Analysis:\r\n");
    printf("Max Gain: %.2f dB at %.1f Hz\r\n", max_magnitude, max_freq);
    printf("Min Gain: %.2f dB at %.1f Hz\r\n", min_magnitude, min_freq);
    printf("Gain Range: %.2f dB\r\n", max_magnitude - min_magnitude);

    // 简单的滤波器类型判断
    float gain_variation = max_magnitude - min_magnitude;
    if (gain_variation > 10.0f) {
        printf("Filter Type: Significant frequency response variation detected\r\n");
    } else if (max_magnitude > -1.0f) {
        printf("Filter Type: Likely passband region\r\n");
    } else if (max_magnitude < -10.0f) {
        printf("Filter Type: Likely stopband region\r\n");
    } else {
        printf("Filter Type: Transition region\r\n");
    }

    printf("Note: Complete analysis requires full sweep data\r\n");
    printf("=== END OF ANALYSIS ===\r\n");
}

void DetermineFilterType(void)
{
    // 判断滤波器类型的标准：
    // 1、低通滤波器：1kHz和1.2kHz的归一化电压幅度比均大于0.7且399.8kHz和400kHz的归一化电压幅度比均小于0.7
    // 2、高通滤波器：1kHz和1.2kHz的归一化电压幅度比均小于0.7且399.8kHz和400kHz的归一化电压幅度比均大于0.7
    // 3、带通滤波器：1kHz和1.2kHz的归一化电压幅度比均小于0.7且399.8kHz和400kHz的归一化电压幅度比均小于0.7
    // 4、带阻滤波器：1kHz和1.2kHz的归一化电压幅度比均大于0.7且399.8kHz和400kHz的归一化电压幅度比均大于0.7

    char filter_type[50] = "Unknown";
    uint16_t display_color = RED;

    // 检查是否有足够的数据进行判断
//    if (freq_1kHz_ratio == 0.0f || freq_1_2kHz_ratio == 0.0f ||
//        freq_399_8kHz_ratio == 0.0f || freq_400kHz_ratio == 0.0f) {
//        sprintf(filter_type, "Insufficient Data");
//        display_color = GRAY;
//    } else 
{
        // 判断低频段（1kHz和1.2kHz）
        uint8_t low_freq_high = (freq_1kHz_ratio > 0.7f) && (freq_1_2kHz_ratio > 0.7f);

        // 判断高频段（399.8kHz和400kHz）
        uint8_t high_freq_high = (freq_399_8kHz_ratio > 0.7f) && (freq_400kHz_ratio > 0.7f);

        if (low_freq_high && !high_freq_high) {
            // 低频高，高频低 -> 低通滤波器
            sprintf(filter_type, "Low-Pass Filter");
            display_color = GREEN;
        } else if (!low_freq_high && high_freq_high) {
            // 低频低，高频高 -> 高通滤波器
            sprintf(filter_type, "High-Pass Filter");
            display_color = BLUE;
        } else if (!low_freq_high && !high_freq_high) {
            // 低频低，高频低 -> 带通滤波器
            sprintf(filter_type, "Band-Pass Filter");
            display_color = MAGENTA;
        } else if (low_freq_high && high_freq_high) {
            // 低频高，高频高 -> 带阻滤波器
            sprintf(filter_type, "Band-Stop Filter");
            display_color = YELLOW;
        }
    }

    // 在LCD屏幕上显示滤波器类型
    // 清除之前的显示区域
    lcd_fill(0, 110, lcddev.width, 150, WHITE);

    // 显示滤波器类型
    lcd_show_string(10, 110, lcddev.width, 20, 16, "Filter Type:", BLACK);
    lcd_show_string(10, 130, lcddev.width, 20, 16, filter_type, display_color);

    // 显示具体的测量值（调试信息）
    char debug_info[100];
    sprintf(debug_info, "1kHz:%.3f 1.2k:%.3f", freq_1kHz_ratio, freq_1_2kHz_ratio);
    lcd_show_string(10, 150, lcddev.width, 20, 12, debug_info, BLACK);

    sprintf(debug_info, "399.8k:%.3f 400k:%.3f", freq_399_8kHz_ratio, freq_400kHz_ratio);
    lcd_show_string(10, 170, lcddev.width, 20, 12, debug_info, BLACK);

    // 通过串口输出结果
    printf("=== FILTER TYPE DETERMINATION ===\r\n");
    printf("Filter Type: %s\r\n", filter_type);
    printf("Amplitude Multiplier Used: %dx\r\n", amplitude_multiplier);
    printf("First Sweep Low Freq Data:\r\n");
    printf("  1kHz (raw): %.6f\r\n", first_sweep_1kHz_ratio);
    printf("  1.2kHz (raw): %.6f\r\n", first_sweep_1_2kHz_ratio);
    printf("Final Normalized Data:\r\n");
    printf("  1kHz: %.6f\r\n", freq_1kHz_ratio);
    printf("  1.2kHz: %.6f\r\n", freq_1_2kHz_ratio);
    printf("  399.8kHz: %.6f\r\n", freq_399_8kHz_ratio);
    printf("  400kHz: %.6f\r\n", freq_400kHz_ratio);
    printf("=== END OF DETERMINATION ===\r\n");

    // 在滤波器类型确定后，计算传递函数参数并显示
    IdentifyCircuitModel();
}

/**
 * @brief  初始化平滑滤波器
 * @param  None
 * @retval None
 * @note   清空所有平滑滤波器的缓冲区和计数器
 */
void InitSmoothFilters(void)
{
    // 清空第二次扫频平滑缓冲区
    for (int i = 0; i < SMOOTH_FILTER_SIZE; i++) {
        smooth_buffer_phase2[i] = 0.0f;
        smooth_buffer_phase3[i] = 0.0f;
    }

    // 重置索引和计数器
    smooth_index_phase2 = 0;
    smooth_index_phase3 = 0;
    smooth_count_phase2 = 0;
    smooth_count_phase3 = 0;
}

/**
 * @brief  应用移动平均平滑滤波器
 * @param  new_value: 新的输入值
 * @param  buffer: 滤波器缓冲区指针
 * @param  index: 缓冲区索引指针
 * @param  count: 有效数据计数指针
 * @param  buffer_size: 缓冲区大小
 * @retval 平滑后的输出值
 * @note   使用移动平均算法对输入数据进行平滑处理
 */
float ApplySmoothFilter(float new_value, float* buffer, uint8_t* index, uint8_t* count, uint8_t buffer_size)
{
    // 将新值存入缓冲区
    buffer[*index] = new_value;

    // 更新索引（循环缓冲区）
    *index = (*index + 1) % buffer_size;

    // 更新有效数据计数（最多为缓冲区大小）
    if (*count < buffer_size) {
        (*count)++;
    }

    // 计算移动平均值
    float sum = 0.0f;
    for (int i = 0; i < *count; i++) {
        sum += buffer[i];
    }

    return sum / (*count);
}

/**
 * @brief  识别电路模型并计算传递函数参数
 * @param  None
 * @retval None
 * @note   根据滤波器类型和扫频结果计算传递函数参数
 */
void IdentifyCircuitModel(void)
{
    printf("=== CIRCUIT MODEL IDENTIFICATION ===\r\n");

    // 检查是否有足够的数据
    if (max_voltage_ratio <= 0.0f) {
        printf("Error: No valid sweep data available\r\n");
        return;
    }

    // 计算滤波器参数
    CalculateFilterParameters();

    // 显示传递函数
    DisplayTransferFunction();

    printf("=== END OF IDENTIFICATION ===\r\n");
}

/**
 * @brief  计算二阶RLC滤波器参数
 * @param  None
 * @retval None
 * @note   基于第二次扫频的未归一化结果计算K、ω₀、Q参数
 */
void CalculateFilterParameters(void)
{
    // 通带增益K（使用第二次扫频的最大值，未归一化）
    float K = max_voltage_ratio;

    // 初始化参数
    float omega_0 = 2.0f * 3.14159f * 10000.0f; // 默认值
    float Q = 0.707f; // 默认品质因数
    float f_0 = 10000.0f; // 默认频率

    // 根据滤波器类型进行参数计算
    if (freq_1kHz_ratio > 0.7f && freq_1_2kHz_ratio > 0.7f &&
        freq_399_8kHz_ratio < 0.7f && freq_400kHz_ratio < 0.7f) {
        // 低通滤波器参数计算
        printf("Calculating Low-Pass Filter Parameters...\r\n");

        // 使用实际扫频数据寻找截止频率
        f_0 = FindCutoffFrequency();

        // 如果找不到合适的截止频率，使用经验估算
        if (f_0 < 1000.0f || f_0 > 500000.0f) {
            // 基于高频衰减率估算截止频率
            float high_freq_avg = (freq_399_8kHz_ratio + freq_400kHz_ratio) / 2.0f;

            if (high_freq_avg > 0.0f && K > 0.0f) {
                float attenuation_400k = 20.0f * log10f(high_freq_avg / K); // dB

                // 二阶低通：-40dB/decade衰减
                // 根据400kHz处的衰减推算截止频率
                if (attenuation_400k < -40.0f) {
                    f_0 = 20000.0f; // 20kHz
                } else if (attenuation_400k < -30.0f) {
                    f_0 = 40000.0f; // 40kHz
                } else if (attenuation_400k < -20.0f) {
                    f_0 = 80000.0f; // 80kHz
                } else if (attenuation_400k < -10.0f) {
                    f_0 = 160000.0f; // 160kHz
                } else {
                    f_0 = 200000.0f; // 200kHz
                }
            } else {
                f_0 = 50000.0f; // 默认50kHz
            }
        }

        omega_0 = 2.0f * 3.14159f * f_0;
        Q = 0.707f; // 二阶巴特沃斯响应的标准值
        DisplayFilterParameters(K, omega_0, Q, "Low-Pass");

    } else if (freq_1kHz_ratio < 0.7f && freq_1_2kHz_ratio < 0.7f &&
               freq_399_8kHz_ratio > 0.7f && freq_400kHz_ratio > 0.7f) {
        // 高通滤波器参数计算
        printf("Calculating High-Pass Filter Parameters...\r\n");

        // 使用实际扫频数据寻找截止频率
        f_0 = FindCutoffFrequency();

        // 如果找不到合适的截止频率，使用经验估算
        if (f_0 < 100.0f || f_0 > 100000.0f) {
            // 基于低频衰减率估算截止频率
            float low_freq_avg = (freq_1kHz_ratio + freq_1_2kHz_ratio) / 2.0f;

            if (low_freq_avg > 0.0f && K > 0.0f) {
                float attenuation_1k = 20.0f * log10f(low_freq_avg / K); // dB

                // 二阶高通：+40dB/decade衰减（低频）
                // 根据1kHz处的衰减推算截止频率
                if (attenuation_1k < -40.0f) {
                    f_0 = 20000.0f; // 20kHz
                } else if (attenuation_1k < -30.0f) {
                    f_0 = 10000.0f; // 10kHz
                } else if (attenuation_1k < -20.0f) {
                    f_0 = 5000.0f; // 5kHz
                } else if (attenuation_1k < -10.0f) {
                    f_0 = 2500.0f; // 2.5kHz
                } else {
                    f_0 = 1000.0f; // 1kHz
                }
            } else {
                f_0 = 5000.0f; // 默认5kHz
            }
        }

        omega_0 = 2.0f * 3.14159f * f_0;
        Q = 0.707f; // 二阶巴特沃斯响应的标准值
        DisplayFilterParameters(K, omega_0, Q, "High-Pass");

    } else if (freq_1kHz_ratio < 0.7f && freq_1_2kHz_ratio < 0.7f &&
               freq_399_8kHz_ratio < 0.7f && freq_400kHz_ratio < 0.7f) {
        // 带通滤波器参数计算
        printf("Calculating Band-Pass Filter Parameters...\r\n");

        // 对于带通滤波器，寻找最大增益点作为中心频率
        if (total_sweep_points > 10) {
            float max_ratio = 0.0f;
            float center_freq = 50000.0f;
            int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

            // 寻找最大增益点
            for (int i = 0; i < valid_points; i++) {
                if (sweep_results[i].adc2_amplitude > max_ratio) {
                    max_ratio = sweep_results[i].adc2_amplitude;
                    center_freq = sweep_results[i].frequency;
                }
            }
            f_0 = center_freq;
        } else {
            f_0 = 50000.0f; // 默认50kHz
        }

        omega_0 = 2.0f * 3.14159f * f_0;

        // 品质因数Q的计算：基于-3dB带宽
        // 对于带通滤波器，Q = f0/BW
        Q = 5.0f; // 中等Q值，实际应根据带宽计算

        DisplayFilterParameters(K, omega_0, Q, "Band-Pass");

    } else if (freq_1kHz_ratio > 0.7f && freq_1_2kHz_ratio > 0.7f &&
               freq_399_8kHz_ratio > 0.7f && freq_400kHz_ratio > 0.7f) {
        // 带阻滤波器参数计算
        printf("Calculating Band-Stop Filter Parameters...\r\n");

        // 对于带阻滤波器，寻找最小增益点作为陷波频率
        if (total_sweep_points > 10) {
            float min_ratio = 1000.0f;
            float notch_freq = 50000.0f;
            int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

            // 寻找最小增益点
            for (int i = 0; i < valid_points; i++) {
                if (sweep_results[i].adc2_amplitude < min_ratio && sweep_results[i].adc2_amplitude > 0.0f) {
                    min_ratio = sweep_results[i].adc2_amplitude;
                    notch_freq = sweep_results[i].frequency;
                }
            }
            f_0 = notch_freq;
        } else {
            f_0 = 50000.0f; // 默认50kHz
        }

        omega_0 = 2.0f * 3.14159f * f_0;

        // 带阻滤波器的Q值决定陷波的尖锐程度
        Q = 10.0f; // 较高Q值产生尖锐陷波

        DisplayFilterParameters(K, omega_0, Q, "Band-Stop");

    } else {
        // 未知类型，使用默认参数
        printf("Unknown filter type, using default parameters\r\n");
        DisplayFilterParameters(K, omega_0, Q, "Unknown");
    }
}

/**
 * @brief  显示传递函数
 * @param  None
 * @retval None
 * @note   根据滤波器类型在LCD上显示相应的传递函数
 */
void DisplayTransferFunction(void)
{
    // 清除传递函数显示区域
    lcd_fill(0, 190, lcddev.width, 270, WHITE);

    // 显示传递函数标题
    lcd_show_string(10, 190, lcddev.width, 20, 14, "Transfer Function:", BLACK);

    // 根据滤波器类型显示相应的传递函数
    if (freq_1kHz_ratio > 0.7f && freq_1_2kHz_ratio > 0.7f &&
        freq_399_8kHz_ratio < 0.7f && freq_400kHz_ratio < 0.7f) {
        // 低通滤波器: H(s) = K * ω₀² / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K * w0^2", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else if (freq_1kHz_ratio < 0.7f && freq_1_2kHz_ratio < 0.7f &&
               freq_399_8kHz_ratio > 0.7f && freq_400kHz_ratio > 0.7f) {
        // 高通滤波器: H(s) = K * s² / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K * s^2", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else if (freq_1kHz_ratio < 0.7f && freq_1_2kHz_ratio < 0.7f &&
               freq_399_8kHz_ratio < 0.7f && freq_400kHz_ratio < 0.7f) {
        // 带通滤波器: H(s) = K * ω₀/Q*s / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K * w0/Q*s", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else if (freq_1kHz_ratio > 0.7f && freq_1_2kHz_ratio > 0.7f &&
               freq_399_8kHz_ratio > 0.7f && freq_400kHz_ratio > 0.7f) {
        // 带阻滤波器: H(s) = K * (s² + ω₀²) / (s² + ω₀/Q*s + ω₀²)
        lcd_show_string(10, 210, lcddev.width, 20, 12, "H(s) = K*(s^2+w0^2)", BLUE);
        lcd_show_string(10, 225, lcddev.width, 20, 12, "     ---------------", BLUE);
        lcd_show_string(10, 240, lcddev.width, 20, 12, "     s^2+w0/Q*s+w0^2", BLUE);

    } else {
        // 未知类型
        lcd_show_string(10, 210, lcddev.width, 20, 12, "Unknown Filter Type", RED);
    }
}

/**
 * @brief  显示滤波器参数
 * @param  K: 通带增益
 * @param  omega_0: 中心/截止角频率 (rad/s)
 * @param  Q: 品质因数
 * @param  filter_type: 滤波器类型字符串
 * @retval None
 * @note   在LCD和串口上显示计算得到的滤波器参数
 */
void DisplayFilterParameters(float K, float omega_0, float Q, const char* filter_type)
{
    char param_buffer[100];

    // 计算频率 (Hz)
    float f_0 = omega_0 / (2.0f * 3.14159f);

    // 在LCD上显示参数（在传递函数下方）
    lcd_show_string(10, 255, lcddev.width, 20, 14, "Parameters:", BLACK);

    // 显示K值
    sprintf(param_buffer, "K = %.3f", K);
    lcd_show_string(10, 275, lcddev.width, 20, 12, param_buffer, GREEN);

    // 显示ω₀值
    sprintf(param_buffer, "w0 = %.1f rad/s", omega_0);
    lcd_show_string(10, 290, lcddev.width, 20, 12, param_buffer, GREEN);

    // 显示f₀值
    sprintf(param_buffer, "f0 = %.1f Hz", f_0);
    lcd_show_string(10, 305, lcddev.width, 20, 12, param_buffer, GREEN);

    // 显示Q值
    sprintf(param_buffer, "Q = %.3f", Q);
    lcd_show_string(10, 320, lcddev.width, 20, 12, param_buffer, GREEN);

    // 通过串口输出详细参数信息
    printf("=== FILTER PARAMETERS (基于第二次扫频未归一化结果) ===\r\n");
    printf("Filter Type: %s\r\n", filter_type);
    printf("通带增益 K = %.6f (由IdentifyCircuitModel函数测得)\r\n", K);
    printf("中心/截止角频率 omega_0 = %.2f rad/s\r\n", omega_0);
    printf("中心/截止频率 f_0 = %.2f Hz (其中 f_0 是从幅频曲线上测量出的频率)\r\n", f_0);
    printf("品质因数 Q = %.6f\r\n", Q);

    // 根据滤波器类型显示相应的传递函数参数说明
    if (strstr(filter_type, "Low-Pass") != NULL) {
        printf("低通滤波器传递函数: H(s) = K * omega_0^2 / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s\r\n", omega_0);
        printf("      Q = %.3f (巴特沃斯响应)\r\n", Q);
    } else if (strstr(filter_type, "High-Pass") != NULL) {
        printf("高通滤波器传递函数: H(s) = K * s^2 / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s\r\n", omega_0);
        printf("      Q = %.3f (巴特沃斯响应)\r\n", Q);
    } else if (strstr(filter_type, "Band-Pass") != NULL) {
        printf("带通滤波器传递函数: H(s) = K * (omega_0/Q)*s / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s (中心频率)\r\n", omega_0);
        printf("      Q = %.3f (品质因数，Q越高带宽越窄)\r\n", Q);
    } else if (strstr(filter_type, "Band-Stop") != NULL) {
        printf("带阻滤波器传递函数: H(s) = K * (s^2 + omega_0^2) / (s^2 + omega_0/Q*s + omega_0^2)\r\n");
        printf("其中: omega_0 = 2*pi*f_0 = %.2f rad/s (陷波频率)\r\n", omega_0);
        printf("      Q = %.3f (品质因数，Q越高陷波越尖锐)\r\n", Q);
    }

    printf("说明: 这些参数采用第二次扫频的结果计算，即未归一化的结果\r\n");
    printf("=== END OF PARAMETERS ===\r\n");
}

/**
 * @brief  从扫频数据中寻找截止频率
 * @param  None
 * @retval 截止频率 (Hz)
 * @note   寻找-3dB点（约0.707倍最大值）
 */
float FindCutoffFrequency(void)
{
    if (total_sweep_points < 10) {
        return 10000.0f; // 默认值
    }

    // 寻找最大值
    float max_ratio = 0.0f;
    int max_index = 0;
    int valid_points = (total_sweep_points < SWEEP_BUFFER_SIZE) ? total_sweep_points : SWEEP_BUFFER_SIZE;

    for (int i = 0; i < valid_points; i++) {
        if (sweep_results[i].adc2_amplitude > max_ratio) {
            max_ratio = sweep_results[i].adc2_amplitude;
            max_index = i;
        }
    }

    // 计算-3dB点的目标值
    float target_ratio = max_ratio * 0.707f;

    // 寻找最接近-3dB点的频率
    float cutoff_freq = 10000.0f;
    float min_diff = 1000.0f;

    for (int i = 0; i < valid_points; i++) {
        float diff = fabsf(sweep_results[i].adc2_amplitude - target_ratio);
        if (diff < min_diff) {
            min_diff = diff;
            cutoff_freq = sweep_results[i].frequency;
        }
    }

    return cutoff_freq;
}

/**
 * @brief  计算品质因数Q
 * @param  f0: 中心频率 (Hz)
 * @param  f1: 下-3dB频率 (Hz)
 * @param  f2: 上-3dB频率 (Hz)
 * @retval 品质因数Q
 * @note   Q = f0 / (f2 - f1)，其中f2-f1是-3dB带宽
 */
float CalculateQFactor(float f0, float f1, float f2)
{
    if (f2 <= f1 || f0 <= 0) {
        return 0.707f; // 默认值
    }

    float bandwidth = f2 - f1;
    float Q = f0 / bandwidth;

    // 限制Q值在合理范围内
    if (Q < 0.1f) Q = 0.1f;
    if (Q > 100.0f) Q = 100.0f;

    return Q;
}

// ADC3谐波分析函数实现
void ProcessADC3HarmonicAnalysis(void)
{
    // 计算ADC3的直流分量
    float adc3_dc = 0;
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        adc3_dc += adc3_sample_buffer[i];
    }
    adc3_dc /= ADC3_SAMPLE_SIZE;

    // 将ADC3数据转换为FFT输入格式（去除直流分量）
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        fft_inputbuf[2*i] = (float)(adc3_sample_buffer[i] - adc3_dc) * (3.3f / 4096.0f);
        fft_inputbuf[2*i+1] = 0.0f;  // 虚部为0
    }

    // 应用汉宁窗
    for (int i = 0; i < ADC3_SAMPLE_SIZE; i++) {
        float window = Hanningwindow(i);
        fft_inputbuf[2*i] *= window;
    }

    // 执行FFT
    arm_cfft_radix4_f32(&scfft, fft_inputbuf);

    // 计算幅度谱
    arm_cmplx_mag_f32(fft_inputbuf, fft_outputbuf, ADC3_SAMPLE_SIZE);

    // 输出谐波分析结果
    OutputHarmonicResults();
}

void OutputHarmonicResults(void)
{
    // 寻找基频（最大峰值，排除直流分量）
    float max_magnitude = 0.0f;
    int fundamental_idx = 1;  // 从索引1开始，跳过直流分量

    // 寻找基频峰值（在前半部分频谱中）
    for (int i = 5; i < ADC3_SAMPLE_SIZE/2; i++) {  // 从索引5开始，避免低频噪声
        if (fft_outputbuf[i] > max_magnitude) {
            max_magnitude = fft_outputbuf[i];
            fundamental_idx = i;
        }
    }

    // 计算基频频率
    float fundamental_freq = (float)fundamental_idx * sampfre / ADC3_SAMPLE_SIZE;

    // 输出谐波分析结果标题
    printf("=== ADC3 HARMONIC ANALYSIS ===\r\n");
    printf("Sampling Frequency: %.0f Hz\r\n", sampfre);
    printf("FFT Size: %d points\r\n", ADC3_SAMPLE_SIZE);
    printf("Frequency Resolution: %.2f Hz\r\n", sampfre / ADC3_SAMPLE_SIZE);
    printf("Fundamental Frequency: %.2f Hz (Index: %d)\r\n", fundamental_freq, fundamental_idx);
    printf("Harmonic\tFrequency(Hz)\tMagnitude\tMagnitude(dB)\r\n");

    // 输出基频到十次谐波的信息
    for (int harmonic = 1; harmonic <= 10; harmonic++) {
        int harmonic_idx = fundamental_idx * harmonic;

        // 检查谐波索引是否在有效范围内
        if (harmonic_idx < ADC3_SAMPLE_SIZE/2) {
            float harmonic_freq = fundamental_freq * harmonic;
            float harmonic_magnitude = fft_outputbuf[harmonic_idx];

            // 计算相对于基频的dB值
            float magnitude_db = 0.0f;
            if (harmonic_magnitude > 0.0f && max_magnitude > 0.0f) {
                magnitude_db = 20.0f * log10f(harmonic_magnitude / max_magnitude);
            } else {
                magnitude_db = -100.0f;  // 很小的值
            }

            // 输出谐波信息
            printf("%d\t\t%.2f\t\t%.6f\t%.2f\r\n",
                   harmonic, harmonic_freq, harmonic_magnitude, magnitude_db);
        } else {
            // 谐波频率超出采样频率范围
            printf("%d\t\t%.2f\t\tOut of Range\t-\r\n",
                   harmonic, fundamental_freq * harmonic);
        }
    }

    printf("=== END OF ADC3 HARMONIC ANALYSIS ===\r\n");

    // 在LCD上显示基频信息
    char lcd_info[100];
    sprintf(lcd_info, "ADC3: Fund=%.1fHz", fundamental_freq);
    lcd_show_string(10, 90, lcddev.width, 20, 12, lcd_info, BLUE);
}