{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "d:/libraries/project_2025_G_KQF_gaibiandac/project_2025_G_dac_100kHz (3)/project_2025_G_dac_100kHz/project_2025_G/USER", "program": "d:/libraries/project_2025_G_KQF_gaibiandac/project_2025_G_dac_100kHz (3)/project_2025_G_dac_100kHz/project_2025_G/USER/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}